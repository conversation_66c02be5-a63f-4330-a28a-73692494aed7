import React from 'react';
import { ParsedDeepAnalysis } from '@/services/openRouter/types/parsedOutput';
import { FormattedContentDisplay } from './ContentFormatter';
import { ActionableListSection } from './ActionableListSection';
import { MessageSquare, HelpCircle } from 'lucide-react';

interface DeepAnalysisDisplayProps {
  analysis: ParsedDeepAnalysis;
  onFollowUpQuestion: (question: string) => void;
  onItemSelectForChat?: (item: string) => void;
  selectedItems?: string[];
  onItemToggleSelect?: (item: string) => void;
}

const Section: React.FC<{ title: string; content?: string }> = ({ title, content }) => {
  if (!content) return null;
  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold mb-4 text-slate-800 border-b border-slate-200 pb-2 flex items-center">
        <div className="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
        {title}
      </h3>
      <FormattedContentDisplay content={content} />
    </div>
  );
};

export const DeepAnalysisDisplay: React.FC<DeepAnalysisDisplayProps> = ({ analysis, onFollowUpQuestion, onItemSelectForChat, selectedItems, onItemToggleSelect }) => {
  const handleItemSelect = onItemSelectForChat ?? onFollowUpQuestion;
  
  return (
    <div className="space-y-8">
      <Section title="Summary" content={analysis.summary} />
      <Section title="Key Concepts" content={analysis.key_concepts} />
      <Section title="Main Argument" content={analysis.main_argument} />
      <Section title="Counterarguments" content={analysis.counterarguments} />
      <Section title="Assumptions" content={analysis.assumptions} />

      <ActionableListSection
        title="Possible Responses You Might Hear"
        description="Here are realistic responses the other person might give:"
        items={analysis.possibleResponses || []}
        onItemSelect={handleItemSelect}
        IconComponent={MessageSquare}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />
      
      <ActionableListSection
        title="Follow-Up Questions"
        description="Use these AI-generated questions to continue the conversation:"
        items={analysis.followUpQuestions || []}
        onItemSelect={handleItemSelect}
        IconComponent={HelpCircle}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />
    </div>
  );
};
